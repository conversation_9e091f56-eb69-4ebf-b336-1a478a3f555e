package id.co.bri.brimo.ui.fragments.pin.reskin

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Color
import android.net.Uri
import android.view.Gravity
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.core.view.WindowCompat
import androidx.recyclerview.widget.GridLayoutManager
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.PinNumberAdapterNewSkin
import id.co.bri.brimo.adapters.baseadapter.base.BasePinAdapter.PinAdapterListener
import id.co.bri.brimo.adapters.pinadapter.OtpInputAdapterNewSkin
import id.co.bri.brimo.contract.IPresenter.IPinPresenter
import id.co.bri.brimo.contract.IView.IPinView
import id.co.bri.brimo.databinding.FragmentPinReskinBinding
import id.co.bri.brimo.di.modules.fragment.PinAllModule
import id.co.bri.brimo.ui.activities.newskinonboarding.OnboardingInputNumberForgetPassActivity
import id.co.bri.brimo.ui.customviews.pin.InsertPinNumbers.Companion.getPinNumberList
import id.co.bri.brimo.ui.fragments.BasePopUpFragment
import javax.inject.Inject

@SuppressLint("ViewConstructor")
class PinReskinFragment (
    private val context: Context,
    private val mSendPin: SendPin,
    private val isEditFastMenu: Boolean = false
): BasePopUpFragment(context), View.OnClickListener, IPinView, PinNumberAdapterNewSkin.OnPinNumberListener,
    PinAdapterListener {
    private var _binding: FragmentPinReskinBinding? = null
    protected val binding get() = _binding!!

    @Inject
    lateinit var presenter: IPinPresenter<IPinView>

    private var onDismissListener: OnDismissListenerSuccessOrNo? = null
    private lateinit var otpInputAdapter: OtpInputAdapterNewSkin

    init {
        injectDependency()
        initProperties()
        setupView()
    }

    override fun createContentView(parent: ViewGroup?): View? {
        _binding = FragmentPinReskinBinding.inflate(LayoutInflater.from(parent?.context), parent, false)

        val lp = LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT)
        lp.gravity = Gravity.BOTTOM
        binding.root.setLayoutParams(lp)
        binding.tvLupaPin.setOnClickListener(this)
        binding.llBack.setOnClickListener(this)

        return binding.root
    }

    fun injectDependency() {
        getActivityComponent()
            .plusPinComponent(PinAllModule())
            .inject(this)
    }

    fun setupView() {
        setupAdapter()
    }

    fun setupAdapter() {
        otpInputAdapter = OtpInputAdapterNewSkin(context)
        val pinNumberAdapter = PinNumberAdapterNewSkin(getPinNumberList(context))
        val pinOtpLayoutManager = GridLayoutManager(context, 6)
        val pinPadLayoutManager = GridLayoutManager(context, 3)

        pinNumberAdapter.onPinNumberListener = this
        otpInputAdapter.setListener(this)
//        binding.tvLupaPin.setOnClickListener {
//            v -> OnboardingInputNumberForgetPassActivity.launchIntent(
//            baseActivity, true
//            )
//        }

        binding.rvBox.layoutManager = pinOtpLayoutManager
        binding.rvBox.adapter = otpInputAdapter

        binding.rvInput.layoutManager = pinPadLayoutManager
        binding.rvInput.adapter = pinNumberAdapter
    }

    fun onButtonPressed(uri: Uri) {
        // do nothing
    }

    fun initProperties() {
        this.tintColor = resources.getColor(R.color.blurry_background)
        this.isDismissOnClickBack = true
        this.isDismissOnTouchBackground = false
        this.blurRadius = 20f
    }

    override fun onShow() {
        super.onShow()

        // Configure status bar to be white and extend fragment to top
        if (baseActivity != null) {
            val window = baseActivity.window
            // Make status bar white to match the fragment background
            window.statusBarColor = Color.WHITE

            // Use modern approach for window flags
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)

            // Set status bar icons to dark (since background is white)
            val windowInsetsController = WindowCompat.getInsetsController(window, window.decorView)
            windowInsetsController.isAppearanceLightStatusBars = true
        }
    }

    override fun onDismiss() {
        super.onDismiss()

        // Restore the original status bar configuration to show the activity's background
        if (baseActivity != null) {
            val window = baseActivity.window
            // Make status bar transparent so the background_confirmation_reskin shows through
            window.statusBarColor = Color.TRANSPARENT

            // Use modern approach for window flags
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)

            // Set status bar icons to light (since the background is likely dark/blue)
            val windowInsetsController = WindowCompat.getInsetsController(window, window.decorView)
            windowInsetsController.isAppearanceLightStatusBars = false
        }
    }

    override fun onClick(view: View?) {
        val id = view?.id
        when (id) {
            R.id.tv_lupa_pin -> {
                dismiss()
                mSendPin.onLupaPin()
            }

            R.id.ll_back -> {
                dismiss()
                if (onDismissListener != null) onDismissListener?.onDismissListenerSuccessOrNo(false)
            }

            else -> return
        }
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            dismiss()
            return true
        } else {
            return super.onKeyDown(keyCode, event)
        }
    }

    // IPinView

    override fun onPinSucces() {
        // do nothing
    }

    override fun onWrongPin() {
        // do nothing
    }

    // adapter

    override fun onPinClicked(pinNumber: Int) {
        binding.tvError.visibility = View.GONE
        otpInputAdapter.addPin(pinNumber.toString())
        otpInputAdapter.resetError()
    }

    override fun onDeleteClicked() {
        otpInputAdapter.deletePin()
        binding.tvError.visibility = View.GONE
        otpInputAdapter.resetError()
    }

    override fun notifyChanges() {
        otpInputAdapter.notifyDataSetChanged()
    }

    override fun onComplete(string: String) {
        if (string.isNotEmpty()) {
            dismiss()
            mSendPin.onSendPinComplete(string)
        }
    }

    interface SendPin {
        fun onSendPinComplete(pin: String)
        fun onLupaPin()
    }

    interface OnDismissListenerSuccessOrNo {
        fun onDismissListenerSuccessOrNo(isSuccess: Boolean)
    }
}