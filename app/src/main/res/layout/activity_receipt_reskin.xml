<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <FrameLayout
            android:id="@+id/my_content"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toTopOf="@+id/cl_bottom"
            android:background="@drawable/bg_receipt_basic_ns">
            <ScrollView
                android:id="@+id/scrollview"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:layout_marginTop="16dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    android:layout_marginBottom="32dp">
                    <ImageView
                        android:id="@+id/iv_logo"
                        android:layout_width="62dp"
                        android:layout_height="32dp"
                        android:layout_marginTop="24dp"
                        android:layout_marginBottom="24dp"
                        android:layout_gravity="center"
                        android:visibility="gone"
                        android:src="@drawable/logo_qitta_splash" />
                    <id.co.bri.brimo.ui.widget.ScallopCardView
                        android:id="@+id/scallopCard"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        tools:visibility="visible"
                        android:visibility="gone"
                        app:useDividerCenter="false"
                        android:layout_marginBottom="24dp"
                        app:watermarkDrawable="@drawable/ic_logo_bri_watermark"
                        app:useFooter="false">
                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content">
                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                app:layout_constraintTop_toTopOf="parent"
                                app:layout_constraintLeft_toLeftOf="parent"
                                app:layout_constraintRight_toRightOf="parent"
                                android:padding="16dp"
                                android:gravity="center"
                                android:orientation="vertical">
                                <ImageView
                                    android:id="@+id/iv_icon_transaction"
                                    android:layout_width="120dp"
                                    android:layout_height="120dp"
                                    android:src="@drawable/ic_checklist_receipt_newskin" />
                                <TextView
                                    android:id="@+id/tv_title_transaction"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textColor="@color/text_black_default_ns"
                                    android:textSize="20sp"
                                    android:text="Transaksi Berhasil" />
                                <TextView
                                    android:id="@+id/tv_nominal"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    style="@style/HeadlineNs.smbold"
                                    android:textSize="28sp"
                                    android:layout_marginTop="10dp"
                                    android:layout_marginBottom="10dp"
                                    tools:text="Rp202.000" />
                                <TextView
                                    android:id="@+id/tv_date_time"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    style="@style/BodyNs.mdreg"
                                    android:textColor="#181C21"
                                    android:textSize="14sp"
                                    android:text="20 Juni 2025, 09:41:32 WIB" />
                            </LinearLayout>
                        </androidx.constraintlayout.widget.ConstraintLayout>
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:padding="16dp"
                            android:orientation="vertical">
                            <RelativeLayout
                                android:id="@+id/ll_token"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="16dp"
                                android:layout_marginBottom="16dp"
                                android:padding="16dp"
                                android:background="@drawable/bg_card_receipt_transaction">
                                <LinearLayout
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_alignParentLeft="true"
                                    android:layout_toLeftOf="@+id/iv_copy_clipboard"
                                    android:orientation="vertical">
                                    <TextView
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:layout_weight="1"
                                        android:text="No. Token"
                                        android:textColor="#9CA3AF"
                                        android:textSize="14sp" />
                                    <TextView
                                        android:id="@+id/tv_no_token"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:layout_marginTop="5dp"
                                        android:layout_weight="1"
                                        tools:text="2332 4554 6677 8900"
                                        style="@style/HeadlineNs.smbold"
                                        android:textSize="14sp" />
                                </LinearLayout>
                                <ImageView
                                    android:id="@+id/iv_copy_clipboard"
                                    android:layout_width="17dp"
                                    android:layout_height="17dp"
                                    android:layout_alignParentRight="true"
                                    android:layout_centerVertical="true"
                                    android:src="@drawable/ic_copy_clipboard_ns" />
                            </RelativeLayout>

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="vertical">
                                <TextView
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    style="@style/HeadlineNs.smbold"
                                    android:textSize="14sp"
                                    android:text="Detail Transaksi" />
                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="16dp"
                                    android:padding="16dp"
                                    android:orientation="vertical"
                                    android:background="@drawable/bg_card_receipt_transaction">

                                    <!-- Row 1 -->
                                    <LinearLayout
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:orientation="horizontal"
                                        android:gravity="center_vertical">

                                        <com.google.android.material.imageview.ShapeableImageView
                                            android:id="@+id/iv_account"
                                            android:layout_width="32dp"
                                            android:layout_height="32dp"
                                            android:scaleType="fitCenter"
                                            android:background="@drawable/bg_circle"
                                            app:shapeAppearanceOverlay="@style/CircleImageView"
                                            tools:src="@drawable/ic_bri" />

<!--                                        <ImageView-->
<!--                                            android:layout_width="32dp"-->
<!--                                            android:layout_height="32dp"-->
<!--                                            android:src="@drawable/img_card_bg"-->
<!--                                            android:scaleType="centerCrop"-->
<!--                                            android:clipToOutline="true" />-->

                                        <Space
                                            android:layout_width="12dp"
                                            android:layout_height="0dp" />

                                        <LinearLayout
                                            android:layout_width="0dp"
                                            android:layout_height="wrap_content"
                                            android:layout_weight="1"
                                            android:orientation="vertical">

                                            <TextView
                                                android:id="@+id/tv_name_account"
                                                android:layout_width="match_parent"
                                                android:layout_height="wrap_content"
                                                tools:text="Source Name"
                                                android:paddingBottom="@dimen/size_2dp"
                                                style="@style/HeadlineNs.smbold"
                                                android:textSize="14sp" />

                                            <Space
                                                android:layout_width="0dp"
                                                android:layout_height="2dp" />

                                            <TextView
                                                android:id="@+id/tv_content_account"
                                                android:layout_width="match_parent"
                                                android:layout_height="wrap_content"
                                                tools:text="Source Value"
                                                android:textColor="#181C21"
                                                android:textSize="12sp" />
                                        </LinearLayout>
                                    </LinearLayout>

                                    <!-- Row 2: Divider -->
                                    <LinearLayout
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:orientation="horizontal"
                                        android:gravity="center_vertical"
                                        android:layout_marginTop="8dp"
                                        android:layout_marginBottom="8dp">

                                        <ImageView
                                            android:layout_width="16dp"
                                            android:layout_height="16dp"
                                            android:layout_marginStart="8dp"
                                            android:layout_marginEnd="8dp"
                                            android:src="@drawable/ic_arrow_down_receipt"
                                            android:scaleType="fitCenter" />
                                        <Space
                                            android:layout_width="12dp"
                                            android:layout_height="0dp" />
                                        <View
                                            android:layout_width="0dp"
                                            android:layout_height="1dp"
                                            android:layout_weight="1"
                                            android:background="#E9EEF6" />
                                    </LinearLayout>

                                    <!-- Row 3 -->
                                    <LinearLayout
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:orientation="horizontal"
                                        android:gravity="center_vertical">

                                        <com.google.android.material.imageview.ShapeableImageView
                                            android:id="@+id/iv_transaction"
                                            android:layout_width="32dp"
                                            android:layout_height="32dp"
                                            android:background="@drawable/bg_circle"
                                            android:scaleType="fitCenter"
                                            app:shapeAppearanceOverlay="@style/CircleImageView"
                                            tools:src="@drawable/img_card_bg" />

                                        <Space
                                            android:layout_width="12dp"
                                            android:layout_height="0dp" />

                                        <LinearLayout
                                            android:layout_width="0dp"
                                            android:layout_height="wrap_content"
                                            android:layout_weight="1"
                                            android:orientation="vertical">

                                            <TextView
                                                android:id="@+id/tv_name_transaction"
                                                android:layout_width="match_parent"
                                                android:layout_height="wrap_content"
                                                tools:text="Value from data[1]"
                                                android:paddingBottom="@dimen/size_2dp"
                                                style="@style/HeadlineNs.smbold"
                                                android:textSize="14sp" />

                                            <Space
                                                android:layout_width="0dp"
                                                android:layout_height="2dp" />

                                            <TextView
                                                android:id="@+id/tv_transaction_content"
                                                android:layout_width="match_parent"
                                                android:layout_height="wrap_content"
                                                tools:text="Value from data[2]"
                                                android:textColor="#181C21"
                                                android:textSize="12sp" />
                                        </LinearLayout>
                                    </LinearLayout>

                                </LinearLayout>
                            </LinearLayout>
                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal"
                                android:gravity="center_vertical"
                                android:layout_marginTop="16dp"
                                android:layout_marginBottom="12dp">

                                <TextView
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:text="No. Referensi"
                                    android:textColor="#7B90A6"
                                    android:textSize="14sp" />

                                <TextView
                                    android:id="@+id/tv_no_pref"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    tools:text="Rp2.000"
                                    android:textColor="#181C21"
                                    android:textSize="14sp" />
                            </LinearLayout>

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal"
                                android:gravity="center_vertical"
                                android:layout_marginBottom="12dp">

                                <TextView
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:text="Jenis Transaksi"
                                    android:textColor="#7B90A6"
                                    android:textSize="14sp" />

                                <TextView
                                    android:id="@+id/tv_transaction_type"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    tools:text="Rp2.000"
                                    android:textColor="#181C21"
                                    android:textSize="14sp" />
                            </LinearLayout>

                            <LinearLayout
                                android:id="@+id/ll_note"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal"
                                android:gravity="center_vertical"
                                android:visibility="gone"
                                android:layout_marginBottom="12dp">

                                <TextView
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:text="Catatan"
                                    android:textColor="#7B90A6"
                                    android:textSize="14sp" />

                                <TextView
                                    android:id="@+id/tv_transaction_note"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    tools:text="Rp2.000"
                                    android:textColor="#181C21"
                                    android:textSize="14sp" />
                            </LinearLayout>
                            <View
                                android:id="@+id/v_line_bill_detail"
                                android:layout_width="match_parent"
                                android:layout_height="1dp"
                                android:background="#E9EEF6"
                                android:layout_marginBottom="12dp"
                                android:visibility="gone" />

                            <LinearLayout
                                android:id="@+id/ll_main_more_content"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="vertical"
                                android:visibility="gone">
                                <LinearLayout
                                    android:id="@+id/ll_more_content"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:orientation="vertical"
                                    android:animateLayoutChanges="true"
                                    android:visibility="gone" />

                                <View
                                    android:layout_width="match_parent"
                                    android:layout_height="1dp"
                                    android:background="#E9EEF6"
                                    android:layout_marginBottom="12dp"
                                    android:visibility="visible" />
                            </LinearLayout>

                            <LinearLayout
                                android:id="@+id/ll_show_hide"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="vertical"
                                android:visibility="gone">

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:orientation="horizontal"
                                    android:gravity="center_vertical"
                                    android:layout_marginBottom="12dp">

                                    <TextView
                                        android:layout_width="0dp"
                                        android:layout_height="wrap_content"
                                        android:layout_weight="1"
                                        android:text="Nominal"
                                        android:textColor="#7B90A6"
                                        android:textSize="14sp" />

                                    <TextView
                                        android:id="@+id/tv_nominal_card"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        tools:text="Rp2.000"
                                        android:textColor="#181C21"
                                        android:textSize="14sp" />
                                </LinearLayout>

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:orientation="horizontal"
                                    android:gravity="center_vertical"
                                    android:layout_marginBottom="12dp">

                                    <TextView
                                        android:layout_width="0dp"
                                        android:layout_height="wrap_content"
                                        android:layout_weight="1"
                                        android:text="Biaya Admin"
                                        android:textColor="#7B90A6"
                                        android:textSize="14sp" />

                                    <TextView
                                        android:id="@+id/tv_admin_fee"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        tools:text="Rp2.000"
                                        android:textColor="#181C21"
                                        android:textSize="14sp" />
                                </LinearLayout>

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:visibility="visible"
                                    android:animateLayoutChanges="true"
                                    android:orientation="vertical"
                                    android:paddingVertical="16dp">

                                    <!-- Text pertama -->
                                    <TextView
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:text="Informasi Hubungi Call Center 1500017"
                                        android:textAlignment="center"
                                        android:gravity="center"
                                        android:textSize="12sp" />

                                    <!-- Spacer 16dp -->
                                    <Space
                                        android:layout_width="match_parent"
                                        android:layout_height="16dp" />

                                    <!-- Text kedua -->
                                    <TextView
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:text="Biaya Termasuk PPN (Apabila Dikenakan/Apabila Ada)\nPT. Bank Rakyat Indonesia (Persero) Tbk.\nKantor Pusat BRI - Jakarta Pusat\nNPWP : 01.001.608.7-093.000"
                                        android:textAlignment="center"
                                        android:gravity="center"
                                        android:textSize="12sp" />
                                </LinearLayout>
                            </LinearLayout>

                            <TextView
                                android:id="@+id/btnLihatLebih"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Lihat Detail "
                                android:drawableEnd="@drawable/ic_chevron_down_reskin"
                                android:textColor="#2563EB"
                                android:textStyle="bold"
                                android:textSize="14sp"
                                android:gravity="center"
                                android:layout_gravity="center_horizontal" />

                        </LinearLayout>
                    </id.co.bri.brimo.ui.widget.ScallopCardView>
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="24dp"
                        android:layout_marginBottom="24dp"
                        android:visibility="gone"
                        android:orientation="vertical">

                        <TextView
                            style="@style/Caption1SmallText.SemiBold.NeutralBaseBlack"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:text="@string/footer_revamp" />

                        <TextView
                            android:id="@+id/tv_empty_footer"
                            style="@style/Caption1SmallText.Medium.NeutralLight80"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="15dp"
                            android:gravity="center"
                            android:text="@string/footer_revamp_2" />

                    </LinearLayout>
                </LinearLayout>
            </ScrollView>
        </FrameLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_bottom"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:padding="16dp"
            android:visibility="gone"
            tools:visibility="visible"
            android:background="@color/white"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent">

            <RelativeLayout
                android:id="@+id/btn_share"
                android:layout_width="48dp"
                android:layout_height="48dp"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                android:background="@drawable/circle"
                android:backgroundTint="#E6EEFF">
                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_centerInParent="true"
                    android:src="@drawable/ic_share_ns"/>
            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/btn_download"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:layout_marginStart="12dp"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintLeft_toRightOf="@+id/btn_share"
                app:layout_constraintBottom_toBottomOf="parent"
                android:background="@drawable/circle"
                android:backgroundTint="#E6EEFF">
                <ImageView
                    android:id="@+id/iv_share"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_centerInParent="true"
                    android:src="@drawable/ic_download_ns" />
            </RelativeLayout>

            <Button
                android:id="@+id/btn_submit"
                android:layout_width="0dp"
                android:layout_height="56dp"
                android:layout_marginStart="16dp"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintLeft_toRightOf="@+id/btn_download"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                style="@style/CustomButtonStyle"
                android:text="Selesai"
                android:textSize="16sp"
                android:textAllCaps="false"
                android:background="@drawable/rounded_button_ns"
                android:textColor="@color/selector_text_color_button_primary_ns"
                android:enabled="true" />
        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>